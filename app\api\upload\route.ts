import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('API route called');
    const formData = await request.formData();
    const file = formData.get('file') as File;

    console.log('File received:', {
      name: file?.name,
      type: file?.type,
      size: file?.size,
    });

    if (!file) {
      console.error('No file provided in form data');
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPG, PNG, GIF, and WebP are allowed.' },
        { status: 400 }
      );
    }

    // Validate file size (10MB limit)
    const maxFileSize = 10 * 1024 * 1024;
    if (file.size > maxFileSize) {
      return NextResponse.json(
        { error: 'File size too large. Maximum size is 10MB.' },
        { status: 400 }
      );
    }

    // Upload to the external API
    console.log('Uploading to external API...');

    // Create new FormData with the correct field name
    const newFormData = new FormData();

    // Convert the file to a buffer and create a proper blob
    const fileBuffer = await file.arrayBuffer();
    const fileBlob = new Blob([fileBuffer], { type: file.type });

    newFormData.append('image', fileBlob, file.name);

    // Debug: Log FormData contents
    console.log('Sending FormData with:');
    for (const [key, value] of newFormData.entries()) {
      if (typeof value === 'object' && value !== null && 'type' in value && 'size' in value) {
        console.log(
          `${key}: ${value.constructor.name} - ${(value as any).type} - ${
            (value as any).size
          } bytes`
        );
      } else {
        console.log(`${key}: ${value}`);
      }
    }

    const uploadResponse = await fetch('http://apiupanh.knowledgehub.io.vn/', {
      method: 'POST',
      body: newFormData,
      headers: {
        Origin: 'https://knowledgehub.io.vn',
        Referer: 'https://knowledgehub.io.vn/',
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
    });

    console.log('Upload response status:', uploadResponse.status);
    console.log('Upload response headers:', Object.fromEntries(uploadResponse.headers.entries()));

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      console.error('Upload failed response:', errorText);
      throw new Error(
        `Upload failed with status: ${uploadResponse.status}. Response: ${errorText}`
      );
    }

    const result = await uploadResponse.text();
    console.log('Upload response body:', result);

    // The API should return a direct link
    // If the response is JSON, parse it; if it's a plain URL, use it directly
    let imageUrl: string;
    try {
      const jsonResult = JSON.parse(result);
      imageUrl = jsonResult.url || jsonResult.link || jsonResult;
    } catch {
      // If parsing fails, assume the response is the URL itself
      imageUrl = result.trim();
    }

    return NextResponse.json({
      success: true,
      url: imageUrl,
      message: 'Image uploaded successfully',
    });
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      {
        error: 'Failed to upload image',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
